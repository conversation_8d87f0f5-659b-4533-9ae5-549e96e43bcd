import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:gotcha_mfg_app/core/storage/storage_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

void main() {
  group('StorageService Push Notification Tests', () {
    late StorageServiceImpl storageService;

    setUp(() async {
      // Initialize SharedPreferences with mock values
      SharedPreferences.setMockInitialValues({});
      final sharedPrefs = await SharedPreferences.getInstance();
      const secureStorage = FlutterSecureStorage();
      
      storageService = StorageServiceImpl(secureStorage, sharedPrefs);
    });

    test('should store and retrieve pending push notifications', () async {
      // Arrange
      final testNotification = {
        'type': 'reminder',
        'title': 'Test Notification',
        'body': 'This is a test notification',
        'on_tap_url': '/home',
      };

      // Act
      await storageService.storePendingPushNotification(testNotification);
      final retrievedNotifications = await storageService.getPendingPushNotifications();

      // Assert
      expect(retrievedNotifications.length, 1);
      expect(retrievedNotifications[0]['type'], 'reminder');
      expect(retrievedNotifications[0]['title'], 'Test Notification');
      expect(retrievedNotifications[0]['body'], 'This is a test notification');
      expect(retrievedNotifications[0]['on_tap_url'], '/home');
      expect(retrievedNotifications[0]['stored_timestamp'], isNotNull);
    });

    test('should store multiple pending push notifications', () async {
      // Arrange
      final notification1 = {
        'type': 'reminder',
        'title': 'Notification 1',
      };
      final notification2 = {
        'type': 'update',
        'title': 'Notification 2',
      };

      // Act
      await storageService.storePendingPushNotification(notification1);
      await storageService.storePendingPushNotification(notification2);
      final retrievedNotifications = await storageService.getPendingPushNotifications();

      // Assert
      expect(retrievedNotifications.length, 2);
      expect(retrievedNotifications[0]['type'], 'reminder');
      expect(retrievedNotifications[1]['type'], 'update');
    });

    test('should clear all pending push notifications', () async {
      // Arrange
      final testNotification = {
        'type': 'reminder',
        'title': 'Test Notification',
      };
      await storageService.storePendingPushNotification(testNotification);

      // Act
      await storageService.clearPendingPushNotifications();
      final retrievedNotifications = await storageService.getPendingPushNotifications();

      // Assert
      expect(retrievedNotifications.length, 0);
    });

    test('should return empty list when no pending notifications exist', () async {
      // Act
      final retrievedNotifications = await storageService.getPendingPushNotifications();

      // Assert
      expect(retrievedNotifications.length, 0);
    });

    test('should handle malformed JSON gracefully', () async {
      // Arrange - manually add malformed JSON to SharedPreferences
      final sharedPrefs = await SharedPreferences.getInstance();
      await sharedPrefs.setStringList('pending_push_notifications', ['invalid json', '{"valid": "json"}']);

      // Act
      final retrievedNotifications = await storageService.getPendingPushNotifications();

      // Assert - should only return the valid JSON entry
      expect(retrievedNotifications.length, 1);
      expect(retrievedNotifications[0]['valid'], 'json');
    });
  });
}
